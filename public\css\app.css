/* Banner Slideshow Styles */
.banner-slideshow {
    position: relative;
    overflow: hidden;
}

.banner-slide {
    height: 70vh;
    min-height: 500px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    display: flex;
    align-items: center;
}

.banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.3));
    z-index: 1;
}

.banner-content {
    position: relative;
    z-index: 2;
    animation: slideInUp 1s ease-out;
}

.carousel-indicators {
    bottom: 20px;
}

.carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 5px;
    background-color: rgba(255, 255, 255, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.carousel-indicators button.active {
    background-color: #fff;
    transform: scale(1.2);
}

.carousel-control-prev,
.carousel-control-next {
    width: 5%;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    width: 30px;
    height: 30px;
    background-size: 100%;
}

/* Animation */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .banner-slide {
        height: 60vh;
        min-height: 400px;
    }

    .banner-content h1 {
        font-size: 2rem !important;
    }

    .banner-content .lead {
        font-size: 1rem;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 8%;
    }
}

@media (max-width: 576px) {
    .banner-slide {
        height: 50vh;
        min-height: 350px;
    }

    .banner-content h1 {
        font-size: 1.75rem !important;
    }

    .banner-content .btn {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
}
