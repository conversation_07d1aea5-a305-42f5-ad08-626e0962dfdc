# 📋 คู่มือการเปลี่ยนโลโก้ (Logo Change Guide)

## 🎯 ตำแหน่งโลโก้ที่สามารถเปลี่ยนได้

### 1. โลโก้ด้านบน (Navigation Bar)
**ตำแหน่ง:** แถบเมนูด้านบนของเว็บไซต์  
**ไฟล์:** `resources/views/layouts/app.blade.php` บรรทัด 31-35  
**รูปภาพ:** `public/images/logo.png`  
**ขนาดแนะนำ:** สูง 40px, กว้างตามสัดส่วน  

```html
<!-- โลโก้หน้าบ้าน - เปลี่ยนรูปภาพได้ที่ public/images/logo.png -->
<a class="navbar-brand fw-bold text-primary d-flex align-items-center" href="{{ route('home') }}">
    <img src="{{ asset('images/logo.png') }}" alt="โลโก้" class="me-2" style="height: 40px; width: auto;">
    <span>{{ $settings['site_name'] ?? 'ผู้ใหญ่ประจักษ์ เซอร์วิส ช็อป' }}</span>
</a>
```

### 2. โลโก้หลักในหน้าแรก (Hero Section)
**ตำแหน่ง:** ส่วนกลางของหน้าแรก (แทนที่รูปดาวเดิม)  
**ไฟล์:** `resources/views/frontend/home.blade.php` บรรทัด 60-67 และ 90-97  
**รูปภาพ:** `public/images/hero-logo.png`  
**ขนาดแนะนำ:** สูงสูงสุด 300px, กว้างตามสัดส่วน  

```html
<!-- โลโก้หลักในหน้าแรก - เปลี่ยนรูปภาพได้ที่ public/images/hero-logo.png -->
<div class="hero-icon-container">
    <img src="{{ asset('images/hero-logo.png') }}" alt="โลโก้หลัก" class="img-fluid" style="max-height: 300px; width: auto;">
</div>
```

## 📁 วิธีการเปลี่ยนโลโก้

### ขั้นตอนที่ 1: เตรียมไฟล์รูปภาพ
1. เตรียมรูปโลโก้ในรูปแบบ PNG, JPG, หรือ SVG
2. ตั้งชื่อไฟล์ตามตำแหน่ง:
   - `logo.png` สำหรับโลโก้ด้านบน
   - `hero-logo.png` สำหรับโลโก้หลักในหน้าแรก

### ขั้นตอนที่ 2: อัปโหลดไฟล์
1. คัดลอกไฟล์รูปภาพไปยังโฟลเดอร์ `public/images/`
2. ตรวจสอบให้แน่ใจว่าชื่อไฟล์ตรงกับที่กำหนด

### ขั้นตอนที่ 3: ตรวจสอบผลลัพธ์
1. รีเฟรชหน้าเว็บไซต์
2. ตรวจสอบว่าโลโก้แสดงผลถูกต้อง

## 🎨 คำแนะนำการออกแบบ

### โลโก้ด้านบน (Navigation)
- **ขนาด:** สูง 40px, กว้างไม่เกิน 200px
- **รูปแบบ:** PNG พื้นหลังโปร่งใส
- **สี:** ควรเข้ากับธีมสีของเว็บไซต์

### โลโก้หลักในหน้าแรก (Hero)
- **ขนาด:** สูงสูงสุด 300px, กว้างตามสัดส่วน
- **รูปแบบ:** PNG หรือ SVG คุณภาพสูง
- **สี:** สามารถใช้สีใดก็ได้ (มี filter ปรับสีอัตโนมัติ)

## 🔧 การปรับแต่งเพิ่มเติม

### เปลี่ยนขนาดโลโก้ด้านบน
แก้ไขใน `resources/views/layouts/app.blade.php`:
```html
<img src="{{ asset('images/logo.png') }}" alt="โลโก้" class="me-2" style="height: 50px; width: auto;">
```

### เปลี่ยนขนาดโลโก้หลัก
แก้ไขใน `resources/views/frontend/home.blade.php`:
```html
<img src="{{ asset('images/hero-logo.png') }}" alt="โลโก้หลัก" class="img-fluid" style="max-height: 400px; width: auto;">
```

### ปิดการใช้งาน Filter สี (สำหรับโลโก้หลัก)
ลบ `filter: brightness(0) invert(1) opacity(0.75);` ออกจาก style

## 📝 หมายเหตุ
- ไฟล์รูปภาพควรมีขนาดไม่เกิน 2MB
- รองรับรูปแบบ: PNG, JPG, JPEG, SVG
- หากต้องการเปลี่ยนชื่อไฟล์ ให้แก้ไขใน code ด้วย
- สำรองไฟล์เดิมก่อนเปลี่ยนแปลง
