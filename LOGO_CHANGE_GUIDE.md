# 📋 คู่มือการเปลี่ยนโลโก้ (Logo Change Guide)

## 🎯 ตำแหน่งโลโก้ที่สามารถเปลี่ยนได้

### 1. โลโก้ด้านบน (Navigation Bar)
**ตำแหน่ง:** แถบเมนูด้านบนของเว็บไซต์  
**ไฟล์:** `resources/views/layouts/app.blade.php` บรรทัด 31-35  
**รูปภาพ:** `public/images/logo.png`  
**ขนาดแนะนำ:** สูง 40px, กว้างตามสัดส่วน  

```html
<!-- โลโก้หน้าบ้าน - เปลี่ยนรูปภาพได้ที่ public/images/logo.png -->
<a class="navbar-brand fw-bold text-primary d-flex align-items-center" href="{{ route('home') }}">
    <img src="{{ asset('images/logo.png') }}" alt="โลโก้" class="me-2" style="height: 40px; width: auto;">
    <span>{{ $settings['site_name'] ?? 'ผู้ใหญ่ประจักษ์ เซอร์วิส ช็อป' }}</span>
</a>
```

### 2. โลโก้หลักในหน้าแรก (Hero Section) - โลโก้ผึ้งทอง 🐝
**ตำแหน่ง:** ส่วนกลางของหน้าแรก (แทนที่รูปดาวเดิม)
**ไฟล์:** `resources/views/frontend/home.blade.php` บรรทัด 62-67 และ 93-97
**รูปภาพ:** `public/images/bee-logo.png`
**ขนาดแนะนำ:** สูงสูงสุด 300px, กว้างตามสัดส่วน
**เอฟเฟกต์พิเศษ:** มีแอนิเมชั่นลอยและเรืองแสงสีทอง

```html
<!-- โลโก้หลักในหน้าแรก - เปลี่ยนรูปโลโก้ผึ้งได้ที่ public/images/bee-logo.png -->
<!-- สำหรับเปลี่ยนโลโก้: ใส่รูปใหม่ในโฟลเดอร์ public/images/ และตั้งชื่อเป็น bee-logo.png -->
<div class="hero-icon-container">
    <img src="{{ asset('images/bee-logo.png') }}" alt="โลโก้ผึ้งทอง - สัญลักษณ์ของความขยันและคุณภาพ" class="img-fluid hero-logo" style="max-height: 300px; width: auto; filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));">
</div>
```

## 📁 วิธีการเปลี่ยนโลโก้

### ขั้นตอนที่ 1: เตรียมไฟล์รูปภาพ
1. เตรียมรูปโลโก้ในรูปแบบ PNG, JPG, หรือ SVG
2. ตั้งชื่อไฟล์ตามตำแหน่ง:
   - `logo.png` สำหรับโลโก้ด้านบน (Navigation Bar)
   - `bee-logo.png` สำหรับโลโก้ผึ้งทองหลักในหน้าแรก (Hero Section)

### ขั้นตอนที่ 2: อัปโหลดไฟล์
1. คัดลอกไฟล์รูปภาพไปยังโฟลเดอร์ `public/images/`
2. ตรวจสอบให้แน่ใจว่าชื่อไฟล์ตรงกับที่กำหนด

### ขั้นตอนที่ 3: ตรวจสอบผลลัพธ์
1. รีเฟรชหน้าเว็บไซต์
2. ตรวจสอบว่าโลโก้แสดงผลถูกต้อง

## 🎨 คำแนะนำการออกแบบ

### โลโก้ด้านบน (Navigation)
- **ขนาด:** สูง 40px, กว้างไม่เกิน 200px
- **รูปแบบ:** PNG พื้นหลังโปร่งใส
- **สี:** ควรเข้ากับธีมสีของเว็บไซต์

### โลโก้ผึ้งทองหลักในหน้าแรก (Hero) 🐝
- **ขนาด:** สูงสูงสุด 300px, กว้างตามสัดส่วน
- **รูปแบบ:** PNG หรือ SVG คุณภาพสูง (แนะนำ PNG พื้นหลังโปร่งใส)
- **สี:** สีทองหรือสีอื่นๆ ที่เข้ากับธีม (มี drop-shadow เพิ่มความสวยงาม)
- **เอฟเฟกต์:** มีแอนิเมชั่นลอย (float) และเอฟเฟกต์ hover ที่สวยงาม

## 🔧 การปรับแต่งเพิ่มเติม

### เปลี่ยนขนาดโลโก้ด้านบน
แก้ไขใน `resources/views/layouts/app.blade.php`:
```html
<img src="{{ asset('images/logo.png') }}" alt="โลโก้" class="me-2" style="height: 50px; width: auto;">
```

### เปลี่ยนขนาดโลโก้ผึ้งทอง
แก้ไขใน `resources/views/frontend/home.blade.php`:
```html
<img src="{{ asset('images/bee-logo.png') }}" alt="โลโก้ผึ้งทอง" class="img-fluid hero-logo" style="max-height: 400px; width: auto;">
```

### ปิดการใช้งาน Drop Shadow (สำหรับโลโก้ผึ้ง)
ลบ `filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));` ออกจาก style

### เปิด/ปิดแอนิเมชั่นลอย
แก้ไขใน `public/css/funeral-style.css` หาส่วน `.hero-icon-container`:
```css
/* ปิดแอนิเมชั่น */
.hero-icon-container {
    /* animation: float 6s ease-in-out infinite; */
}

/* เปิดแอนิเมชั่น */
.hero-icon-container {
    animation: float 6s ease-in-out infinite;
}
```

## 🐝 คำแนะนำพิเศษสำหรับโลโก้ผึ้งทอง

### สีที่เข้ากันดี
- **สีทอง:** #FFD700, #FFC107, #F39C12
- **สีน้ำตาล:** #8B4513, #A0522D, #CD853F
- **สีดำ:** #000000, #2C3E50, #34495E

### เอฟเฟกต์พิเศษ
- **Drop Shadow:** เงาตกกระทบที่สวยงาม
- **Hover Effect:** ขยายและหมุนเล็กน้อยเมื่อเอาเมาส์ชี้
- **Float Animation:** แอนิเมชั่นลอยขึ้นลงอย่างนุ่มนวล

### การปรับแต่งสีเอฟเฟกต์
แก้ไขใน style ของ img tag:
```html
<!-- เรืองแสงสีทอง -->
style="filter: drop-shadow(0 4px 8px rgba(255, 193, 7, 0.5));"

<!-- เรืองแสงสีน้ำเงิน -->
style="filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.5));"

<!-- เงาธรรมดา -->
style="filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));"
```

## 📝 หมายเหตุ
- ไฟล์รูปภาพควรมีขนาดไม่เกิน 2MB
- รองรับรูปแบบ: PNG, JPG, JPEG, SVG
- แนะนำใช้ PNG พื้นหลังโปร่งใสสำหรับโลโก้ผึ้ง
- หากต้องการเปลี่ยนชื่อไฟล์ ให้แก้ไขใน code ด้วย
- สำรองไฟล์เดิมก่อนเปลี่ยนแปลง
- โลโก้ผึ้งจะมีแอนิเมชั่นและเอฟเฟกต์พิเศษอัตโนมัติ
