@extends('layouts.admin')

@section('title', 'แดชบอร์ด - Phuyai Prajak Service Shop Admin')

@section('content')
<!-- Welcome Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">สวัสดี, {{ Auth::user()->name }}! 👋</h1>
                <p class="text-muted mb-0">ยินดีต้อนรับสู่ระบบจัดการ Phuyai Prajak Service Shop</p>
            </div>
            <div class="text-end">
                <small class="text-muted">วันที่: {{ now()->format('d/m/Y H:i') }}</small>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row g-4 mb-4 mt-5">
    <div class="col-md-6 col-lg-3">
        <div class="card stats-card stats-card-square pulse-hover shadow-primary h-100">
            <div class="card-body text-white position-relative d-flex flex-column justify-content-center">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-2 opacity-90">บริการทั้งหมด</h6>
                        <h2 class="mb-0 fw-bold">{{ $stats['services_count'] }}</h2>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-cogs fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3">
        <div class="card stats-card-square bg-success text-white pulse-hover shadow-success h-100">
            <div class="card-body position-relative d-flex flex-column justify-content-center">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-2 opacity-90">แพคเกจทั้งหมด</h6>
                        <h2 class="mb-0 fw-bold">{{ $stats['packages_count'] }}</h2>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-box fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3">
        <div class="card stats-card-square bg-warning text-white pulse-hover shadow-warning h-100">
            <div class="card-body position-relative d-flex flex-column justify-content-center">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-2 opacity-90">ผลงานทั้งหมด</h6>
                        <h2 class="mb-0 fw-bold">{{ $stats['activities_count'] }}</h2>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-images fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3">
        <div class="card stats-card-square bg-info text-white pulse-hover shadow-primary h-100">
            <div class="card-body position-relative d-flex flex-column justify-content-center">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-2 opacity-90">ข้อความติดต่อ</h6>
                        <h2 class="mb-0 fw-bold">{{ $stats['contacts_count'] }}</h2>
                        @if($stats['unread_contacts'] > 0)
                        <small class="opacity-90">{{ $stats['unread_contacts'] }} ข้อความใหม่</small>
                        @endif
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-envelope fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions & Analytics -->
<div class="row g-4 mb-5">
    <!-- Quick Actions -->
    <div class="col-lg-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2 text-warning"></i>การดำเนินการด่วน
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="{{ route('admin.services.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>เพิ่มบริการใหม่
                    </a>
                    <a href="{{ route('admin.packages.create') }}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>เพิ่มแพคเกจใหม่
                    </a>
                    <a href="{{ route('admin.activities.create') }}" class="btn btn-warning">
                        <i class="fas fa-plus me-2"></i>เพิ่มผลงานใหม่
                    </a>
                    <a href="{{ route('admin.settings') }}" class="btn btn-info">
                        <i class="fas fa-cog me-2"></i>ตั้งค่าเว็บไซต์
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart Analytics -->
    <div class="col-lg-8">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2 text-primary"></i>ภาพรวมข้อมูล
                </h5>
            </div>
            <div class="card-body">
                <canvas id="statsChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities & Contacts -->
<div class="row g-4 mb-5">
    <!-- Recent Contacts -->
    <div class="col-lg-6">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-envelope me-2 text-info"></i>ข้อความติดต่อล่าสุด
                </h5>
                <a href="{{ route('admin.contacts') }}" class="btn btn-sm btn-outline-primary">ดูทั้งหมด</a>
            </div>
            <div class="card-body">
                @if($recent_contacts->count() > 0)
                <div class="list-group list-group-flush">
                    @foreach($recent_contacts as $contact)
                    <div class="list-group-item px-0 border-0 {{ !$contact->is_read ? 'bg-primary bg-opacity-10' : '' }}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-1">
                                    <h6 class="mb-0 me-2">{{ $contact->name }}</h6>
                                    @if(!$contact->is_read)
                                    <span class="badge bg-warning text-dark">ใหม่</span>
                                    @endif
                                </div>
                                <p class="mb-1 text-muted small">{{ Str::limit($contact->subject, 50) }}</p>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>{{ $contact->created_at->diffForHumans() }}
                                </small>
                            </div>
                            <a href="{{ route('admin.contacts.show', $contact->id) }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-3x mb-3 opacity-50"></i>
                    <p class="mb-0">ยังไม่มีข้อความติดต่อ</p>
                </div>
                @endif
            </div>
        </div>
    </div>


</div>

@endsection

@section('scripts')
<script>
// กราฟง่าย ๆ
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('statsChart');
    if (ctx) {
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['บริการ', 'แพคเกจ', 'ผลงาน', 'ข้อความ'],
                datasets: [{
                    data: [
                        {{ $stats['services_count'] }},
                        {{ $stats['packages_count'] }},
                        {{ $stats['activities_count'] }},
                        {{ $stats['contacts_count'] }}
                    ],
                    backgroundColor: [
                        '#4fc3f7',
                        '#28a745',
                        '#ffc107',
                        '#17a2b8'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }
});
</script>
@endsection
