<?php $__env->startSection('title', 'จัดการแบนเนอร์ - Phuyai Prajak Service Shop Admin'); ?>

<?php $__env->startSection('content'); ?>
<!-- Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                    <i class="fas fa-images me-2"></i>จัดการแบนเนอร์
                </h1>
                <p class="text-muted mb-0">จัดการแบนเนอร์ทั้งหมดของคุณ</p>
            </div>
            <div class="d-flex gap-2">
                <a href="<?php echo e(route('admin.banners.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>เพิ่มแบนเนอร์ใหม่
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Banners Content -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i>รายการแบนเนอร์
            </h5>
        </div>
    </div>
    <div class="card-body">
        <?php if($banners->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>รูปภาพ</th>
                            <th>ชื่อแบนเนอร์</th>
                            <th>คำอธิบาย</th>
                            <th>แสดงในหน้า</th>
                            <th>ลำดับ</th>
                            <th>สถานะ</th>
                            <th>วันที่สร้าง</th>
                            <th>จัดการ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td>
                                <?php if($banner->image_path): ?>
                                    <img src="<?php echo e(asset('storage/' . $banner->image_path)); ?>"
                                         alt="<?php echo e($banner->title); ?>"
                                         class="img-thumbnail"
                                         style="width: 80px; height: 50px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-light d-flex align-items-center justify-content-center"
                                         style="width: 80px; height: 50px;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td><?php echo e($banner->title); ?></td>
                            <td><?php echo e(Str::limit($banner->description, 50)); ?></td>
                            <td>
                                <span class="badge bg-secondary"><?php echo e($banner->display_pages_name); ?></span>
                            </td>
                            <td><?php echo e($banner->sort_order); ?></td>
                            <td>
                                <?php if($banner->is_active): ?>
                                    <span class="badge bg-success">เปิดใช้งาน</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">ปิดใช้งาน</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo e($banner->created_at->format('d/m/Y H:i')); ?></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('admin.banners.edit', $banner)); ?>"
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="<?php echo e(route('admin.banners.destroy', $banner)); ?>"
                                          method="POST"
                                          class="d-inline"
                                          id="deleteBannerForm<?php echo e($banner->id); ?>">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                onclick="handleDeleteBanner(<?php echo e($banner->id); ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center">
                <?php echo e($banners->links()); ?>

            </div>
        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-images fa-5x text-muted mb-4"></i>
            <h4 class="text-muted mb-2">ยังไม่มีแบนเนอร์</h4>
            <p class="text-muted mb-4">เริ่มต้นด้วยการเพิ่มแบนเนอร์แรกของคุณ</p>
            <a href="<?php echo e(route('admin.banners.create')); ?>" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>เพิ่มแบนเนอร์ใหม่
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
// Delete banner function with custom modal
async function handleDeleteBanner(bannerId) {
    const confirmed = await confirmDelete(
        'คุณแน่ใจหรือไม่ที่จะลบแบนเนอร์นี้? การกระทำนี้ไม่สามารถยกเลิกได้',
        'ยืนยันการลบแบนเนอร์'
    );

    if (confirmed) {
        document.getElementById(`deleteBannerForm${bannerId}`).submit();
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\PhuyaiPrajakserviceshop\resources\views/admin/banners/index.blade.php ENDPATH**/ ?>